
[Extracted from uploads/123456.pdf]
TalentLoop: Agentic AI Job Matching Platform - System Overview
Core Agents & Responsibilities
1. ExtractionAgent
   - Role: Extracts job/resume data from PDFs or screenshots.
   - Tools: PyMuPDF, OCR (if image-based), OpenAI LLM for structured extraction.
2. CandidateAgent
   - Role: Handles candidate profile, job search history, passive matching, and approvals.
   - Tools: MongoDB (profile storage), ChromaDB (resume vector), LangGraph (agent management).
3. EmployerAgent
   - Role: Manages job posting, weight configuration, shortlisting, and interviews.
   - Tools: OpenAI (prompt scoring), LangGraph, MongoDB, Web UI.
4. MatchingAgent
   - Role: Computes match % using embedding + interview data.
   - Tools: OpenAI / HuggingFace embeddings, ChromaDB, LangGraph logic.
5. InterviewTrackerAgent
   - Role: Tracks interview outcome, updates scores, retrains profile embeddings.
   - Tools: MongoDB, LLM (feedback summarization), scoring module.
6. AdminAgent
   - Role: GDPR config, match threshold settings, partner onboarding, audit logs.
   - Tools: <PERSON><PERSON><PERSON>, Auth system, Admin dashboard (React).
Tools by Function
| Function                  | Tools/Frameworks                    |
|---------------------------|-------------------------------------|
| PDF/Text Extraction       | PyMuPDF, OCR, OpenAI LLM            |
| Resume/Job Embeddings     | OpenAI or HuggingFace (BGE, MiniLM) |

TalentLoop: Agentic AI Job Matching Platform - System Overview
| Vector Search             | ChromaDB                            |
| Structured Storage        | MongoDB                             |
| Agentic Workflow          | LangGraph                           |
| LLM Reasoning & Scoring   | OpenAI GPT-4o / Claude              |
| UI                        | React / Streamlit                   |
| Notifications             | Twilio / Email API                  |
| Observability             | LangSmith                           |



[Extracted from uploads/12345678.pdf]
TalentLoop: Agentic AI Job Matching Platform - System Overview
Core Agents & Responsibilities
1. ExtractionAgent
   - Role: Extracts job/resume data from PDFs or screenshots.
   - Tools: PyMuPDF, OCR (if image-based), OpenAI LLM for structured extraction.
2. CandidateAgent
   - Role: Handles candidate profile, job search history, passive matching, and approvals.
   - Tools: MongoDB (profile storage), ChromaDB (resume vector), LangGraph (agent management).
3. EmployerAgent
   - Role: Manages job posting, weight configuration, shortlisting, and interviews.
   - Tools: OpenAI (prompt scoring), LangGraph, MongoDB, Web UI.
4. MatchingAgent
   - Role: Computes match % using embedding + interview data.
   - Tools: OpenAI / HuggingFace embeddings, ChromaDB, LangGraph logic.
5. InterviewTrackerAgent
   - Role: Tracks interview outcome, updates scores, retrains profile embeddings.
   - Tools: MongoDB, LLM (feedback summarization), scoring module.
6. AdminAgent
   - Role: GDPR config, match threshold settings, partner onboarding, audit logs.
   - Tools: LangSmith, Auth system, Admin dashboard (React).
Tools by Function
| Function                  | Tools/Frameworks                    |
|---------------------------|-------------------------------------|
| PDF/Text Extraction       | PyMuPDF, OCR, OpenAI LLM            |
| Resume/Job Embeddings     | OpenAI or HuggingFace (BGE, MiniLM) |

TalentLoop: Agentic AI Job Matching Platform - System Overview
| Vector Search             | ChromaDB                            |
| Structured Storage        | MongoDB                             |
| Agentic Workflow          | LangGraph                           |
| LLM Reasoning & Scoring   | OpenAI GPT-4o / Claude              |
| UI                        | React / Streamlit                   |
| Notifications             | Twilio / Email API                  |
| Observability             | LangSmith                           |



[Extracted from uploads/123.pdf]
TalentLoop Architecture and Technology Overview
Core Technologies Used
1. FastAPI
- Backend framework serving PDF uploads, DB sync, and agent interaction
2. LangGraph + LangChain MCP
- LangGraph: Agent orchestration for structured tool chaining
- MCP: Adapts tools for agent use, enabling multi-modal workflows
3. OpenAI GPT-4o
- Language model for reasoning, planning, and tool invocation
4. ChromaDB
- Vector database for storing job/candidate embeddings
5. MongoDB
- Main structured data store for job and user data
6. Custom Tools (MCP)
- PDF Extractor: Parses job data from PDFs using PyMuPDF/OCR
- Embedder: Converts text into embeddings
- Chroma Tool: Stores embedding and metadata in ChromaDB
- Mongo Tool: Fetches jobs from DB for processing
API Overview
1. /upload-job/
- Accepts PDF and job_id
- Triggers agent to: Extract -> Embed -> Store
2. /sync-jobs-from-db/
- Triggered on employer job post

TalentLoop Architecture and Technology Overview
- Fetch from Mongo -> Embed -> Store in ChromaDB
Agentic Flow Examples
PDF Upload Flow:
PDF -> Extract Tool -> Embed Tool -> ChromaDB Tool
Job Post from MongoDB:
MongoDB Tool -> Embed Tool -> ChromaDB Tool
Multi-Tool Chain Prompt:
"Extract job from path.pdf, embed and store with ID xyz"
-> Agent chains: Extract -> Embed -> Store
Directory Structure
.
-> main.py                  # FastAPI app
-> server/
->   -> pdf_extract_server.py # PDF extraction MCP tool
->   -> embed_server.py       # Embedding MCP tool
-> uploads/                 # Saved PDFs
-> .env                     # Environment variables
-> requirements.txt         # Dependencies
Benefits of Agentic Approach
- No hard-coded logic for workflows
- Agent chooses tools based on prompt
- Scalable and declarative by design
- Easier to expand with more tools
- Prompts become workflow commands



[Extracted from uploads/123.pdf]
TalentLoop Architecture and Technology Overview
Core Technologies Used
1. FastAPI
- Backend framework serving PDF uploads, DB sync, and agent interaction
2. LangGraph + LangChain MCP
- LangGraph: Agent orchestration for structured tool chaining
- MCP: Adapts tools for agent use, enabling multi-modal workflows
3. OpenAI GPT-4o
- Language model for reasoning, planning, and tool invocation
4. ChromaDB
- Vector database for storing job/candidate embeddings
5. MongoDB
- Main structured data store for job and user data
6. Custom Tools (MCP)
- PDF Extractor: Parses job data from PDFs using PyMuPDF/OCR
- Embedder: Converts text into embeddings
- Chroma Tool: Stores embedding and metadata in ChromaDB
- Mongo Tool: Fetches jobs from DB for processing
API Overview
1. /upload-job/
- Accepts PDF and job_id
- Triggers agent to: Extract -> Embed -> Store
2. /sync-jobs-from-db/
- Triggered on employer job post

TalentLoop Architecture and Technology Overview
- Fetch from Mongo -> Embed -> Store in ChromaDB
Agentic Flow Examples
PDF Upload Flow:
PDF -> Extract Tool -> Embed Tool -> ChromaDB Tool
Job Post from MongoDB:
MongoDB Tool -> Embed Tool -> ChromaDB Tool
Multi-Tool Chain Prompt:
"Extract job from path.pdf, embed and store with ID xyz"
-> Agent chains: Extract -> Embed -> Store
Directory Structure
.
-> main.py                  # FastAPI app
-> server/
->   -> pdf_extract_server.py # PDF extraction MCP tool
->   -> embed_server.py       # Embedding MCP tool
-> uploads/                 # Saved PDFs
-> .env                     # Environment variables
-> requirements.txt         # Dependencies
Benefits of Agentic Approach
- No hard-coded logic for workflows
- Agent chooses tools based on prompt
- Scalable and declarative by design
- Easier to expand with more tools
- Prompts become workflow commands


