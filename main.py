from fastapi import FastAP<PERSON>, UploadFile, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from langchain_openai import OpenAIEmbeddings
from fastapi import FastAPI, HTTPException

from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
from langchain_openai import Chat<PERSON>penA<PERSON>
from pymongo import MongoClient
from pprint import pformat
import chromadb
from dotenv import load_dotenv
import os

import logging

logging.basicConfig(
    level=logging.INFO,  # Or DEBUG for more details
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
)

logger = logging.getLogger("main")

# ─────────────────────────────────────────────
# 1. Load environment and prepare paths
# ─────────────────────────────────────────────
load_dotenv()
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")
UPLOAD_DIR = "uploads"
os.makedirs(UPLOAD_DIR, exist_ok=True)

# ─────────────────────────────────────────────
# 2. FastAPI App
# ─────────────────────────────────────────────
app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"]
)

# ─────────────────────────────────────────────
# 3. Initialize MCP client + Agent
# ─────────────────────────────────────────────
@app.on_event("startup")
async def startup():
    logger.info("Starting up MCP agent client")

    client = MultiServerMCPClient({
        "extractor": {
            "command": "python",
            "args": ["server/pdf_extract_server.py"],
            "transport": "stdio"
        },
        "embed": {
            "command": "python",
            "args": ["server/embed_server.py"],
            "transport": "stdio"
        },
        "chroma":{
            "url":"http://localhost:8001/mcp",
            "transport":"streamable_http",
        },
        "mongo": {
            "url":"http://localhost:8000/mcp",
            "transport":"streamable_http",
        },
    })
    tools = await client.get_tools()
    logger.info(f"Retrieved tools from MCP: {', '.join([tool.name for tool in tools])}")

    model = ChatOpenAI(model="gpt-4o")

    app.state.agent = create_react_agent(model, tools)
    # agent_executor = create_react_agent(model, tools, verbose=True)

    logger.info("✅ MCP agent ready")

# ─────────────────────────────────────────────
# 4. API Endpoint to Accept PDF & Run Agent
# ─────────────────────────────────────────────
@app.post("/upload-job/")
async def upload_job(file: UploadFile, job_id: str = Form(...)):
    logger.info(f"Received file upload for job_id: {job_id}, filename: {file.filename}")

    file_path = os.path.join(UPLOAD_DIR, f"{job_id}.pdf")
    with open(file_path, "wb") as f:
        f.write(await file.read())
    logger.info(f"Saved file to {file_path}")

    prompt = (
        f"Extract job details from `{file_path}`.\n"
        f"Embed it and store it in ChromaDB with job_id `{job_id}`.\n"
        f"Return confirmation after completion."
    )
    logger.info("Sending prompt to agent")

    try:
        result = await app.state.agent.ainvoke({"messages": prompt})
        # logger.info(f"Agent result for job_id {job_id}: {result}")
        formatted = format_agent_result(result)
        logger.info(formatted)
        return {"status": "success", "result": result}
    except Exception as e:
        logger.exception(f"Agent failed for job_id {job_id}")

        return JSONResponse(status_code=500, content={"error": str(e)})

@app.post("/sync-jobs-from-db")
async def sync_jobs(job_id: str = Form(...), employer_id: str = Form(...)):
    logger.info(f"Sync requested for job_id={job_id}, employer_id={employer_id}")

    prompt = f"""
    1. Use the `mongo` tool to fetch the job document where `_id="{job_id}"` and `employerId="{employer_id}"`.
    2. Extract the `title`, `description`, and `domain`.
    3. Combine `title` and `description` into: "Title: <title>\\nDescription: <description>".
    4. Use the `chroma` tool to store it with:
        - text: the combined title and description
        - job_id: "{job_id}"
        - metadata:
            - title
            - domain
            - employerId: "{employer_id}"
    5. Return the result from the `chroma` tool.
    """
    logger.info("Sending sync prompt to agent")

    try:
        result = await app.state.agent.ainvoke({"messages": [{"role": "user", "content": prompt}]})
        formatted = format_agent_result(result)
        logger.info(formatted)

        return {"status": "done", "result": result}
    except Exception as e:
        logger.exception(f"Sync failed for job_id={job_id}")
        return JSONResponse(status_code=500, content={"error": str(e)})




def format_agent_result(result):
    if not result or "messages" not in result:
        return "No result or messages found."

    formatted = "\n=== Agent Execution Trace ===\n"
    for idx, message in enumerate(result["messages"]):
        msg_type = type(message).__name__
        content = getattr(message, "content", "")
        tool_calls = getattr(message, "tool_calls", None)

        formatted += f"\n[{idx + 1}] {msg_type}:\n"
        if content:
            formatted += f"{content.strip()}\n"

        if tool_calls:
            for tc in tool_calls:
                formatted += f"\n  → Tool Call: {tc['name']}({tc['args']})\n"

        # Handle ToolMessage
        if msg_type == "ToolMessage":
            formatted += f"\n  → Tool Result: {content.strip()}\n"

    return formatted


@app.get("/list-chroma-collection")
async def list_chroma_docs():
    # import chromadb
    # from chromadb.config import Settings

    client = chromadb.HttpClient(host="localhost", port=9000)
    collection = client.get_collection(name="job_descriptions")

    data = collection.get(limit=100)  # fetch up to 100 entries
    print("data:::::",data)
    return {
        "count": len(data["ids"]),
        "ids": data["ids"],
        "documents": data["documents"],
        "metadatas": data["metadatas"],
    }
@app.delete("/delete-collection/{name}")
async def delete_collection_endpoint(name: str):
    try:
        client = chromadb.HttpClient(host="localhost", port=9000)
        client.delete_collection(name)
        return {"status": "deleted", "collection": name}
    except ValueError:
        raise HTTPException(status_code=404, detail=f"Collection '{name}' not found")
