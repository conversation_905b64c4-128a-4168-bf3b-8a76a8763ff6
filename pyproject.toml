[project]
name = "mcp-server"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "chromadb>=1.0.15",
    "fastapi>=0.116.0",
    "langchain>=0.3.26",
    "langchain-chroma>=0.2.4",
    "langchain-community>=0.3.27",
    "langchain-mcp-adapters>=0.1.8",
    "langchain-openai>=0.3.27",
    "langgraph>=0.5.1",
    "mcp>=1.10.1",
    "pymongo>=4.13.2",
    "pymupdf>=1.26.3",
    "uvicorn>=0.35.0",
]
