from mcp.server.fastmcp import FastMCP
from langchain_chroma import Chroma
from langchain_openai import OpenAIEmbeddings
import chromadb
import uuid
import os
from dotenv import load_dotenv

load_dotenv()

# Initialize MCP server
mcp = FastMCP(name="Chroma",port = 8001)

# Create HTTP ChromaDB client
chroma_client = chromadb.HttpClient(host="localhost", port=9000)

# Use consistent embedding model
embedding_function = OpenAIEmbeddings(
    api_key=os.getenv("OPENAI_API_KEY"),
    model="text-embedding-ada-002"
)

# Use single shared collection for all jobs
COLLECTION_NAME = "job_descriptions"
vectorstore = Chroma(
    client=chroma_client,
    collection_name=COLLECTION_NAME,
    embedding_function=embedding_function
)


@mcp.tool(name="chroma")
def chroma_store(text: str, job_id: str, metadata: dict) -> dict:
    """
    Embeds the job text and stores it in ChromaDB with metadata.

    Args:
        text (str): Combined job title + description
        job_id (str): Job ID from MongoDB
        metadata (dict): Includes title, domain, employerId, etc.

    Returns:
        dict: Storage confirmation
    """
    # Avoid duplicates by generating ID with job_id
    doc_id = f"job_{job_id}"

    # Merge full metadata
    full_metadata = {"job_id": job_id}
    full_metadata.update(metadata)

    # Optional: check if already exists and skip (not enforced by Chroma API yet)
    try:
        vectorstore.add_texts(
            texts=[text],
            metadatas=[full_metadata],
            ids=[doc_id]
        )
        print("✅ Embedded & stored in ChromaDB:", full_metadata)
        return {"status": "stored", "job_id": job_id, "doc_id": doc_id}
    except Exception as e:
        print(f"⚠️ Error storing job: {e}")
        return {"status": "error", "error": str(e)}


print("✅ Chroma MCP Tool Loaded")

if __name__ == "__main__":
    mcp.run(transport="streamable-http")  # Do not use `port=` argument
