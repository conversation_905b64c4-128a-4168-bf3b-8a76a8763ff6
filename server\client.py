from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
from langchain_openai import ChatOpenAI

from dotenv import load_dotenv

import asyncio

async def main():
    client=MultiServerMCPClient(
        {
            "math":{
                "command":"python",
            "args":["pdf_extract_server.py"], ## use Absolute path
            "transport":"stdio",
            },
            "embed":{
                "command":"python",
            "args":["embed_server.py"],
            "transport":"stdio",
            },
            "chroma":{
                "url":"http://localhost:8000/mcp",
                "transport":"streamable_http",

            }


        }
    )

    import os
    os.environ["OPENAI_API_KEY"]= os.getenv("OPENAI_API_KEY")

    tools = await client.get_tools()
    model=ChatOpenAI(model="GPT-4o mini")
    agent=create_react_agent(model,tools)

# print("agent::::::",agent)
print("MCP SERVER ACTIVATED")