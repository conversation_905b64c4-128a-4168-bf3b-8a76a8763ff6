from mcp.server.fastmcp import FastMCP
from langchain_openai import OpenAIEmbeddings
from dotenv import load_dotenv
load_dotenv()

mcp = FastMCP(name="Embedder")
#
@mcp.tool()
def embed_job_text(text: str) -> dict:
    """
    Converts job description text into an embedding vector using OpenAI.

    Args:
        text (str): Job description text

    Returns:
        dict: { "embedding": List[float] }
    """
    import os
    embedding_model = OpenAIEmbeddings(api_key=os.getenv("OPENAI_API_KEY"))
    vector = embedding_model.embed_query(text)
    print("MCP_EMBED_SUCCESSFULLY ")
    return {"embedding": vector}

if __name__ == "__main__":
    mcp.run(transport="stdio")  # Changed transport and added different port
