from mcp.server.fastmcp import FastMCP
from pymongo import MongoClient
from bson import ObjectId  # for handling _id
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("mongo")
mcp = FastMCP(name="mongo")

mongo = MongoClient("mongodb://localhost:27017/")
db = mongo["talentloop"]
jobs_collection = db["jobs"]

# @mcp.tool()
# def fetch_job(job_id: str, employer_id: str) -> dict:
#     # logger.info(f"Tool fetch_job called with job_id={job_id}, employer_id={employer_id}")
#     import sys
#     print("job_d::::", job_id, file=sys.stdout, flush=True)
#
#     """
#     MCP tool: Fetch job document using job_id and employer_id.
#     Returns: {
#         "job_id": ...,
#         "text": "Title: <title>\nDescription: <desc>",
#         "metadata": { "domain": ..., "title": ..., "employerId": ... }
#     }
#     """
#
#     try:
#         job = jobs_collection.find_one({
#             "_id": ObjectId(job_id),
#             "employerId": employer_id  # Keep as string
#         })
#
#
#     except Exception as e:
#         return {"error": f"Invalid ObjectId or DB error: {str(e)}"}
#
#     if not job:
#         return {"error": f"Job {job_id} not found for employer {employer_id}"}
#
#     text = f"Title: {job.get('title', '')}\nDescription: {job.get('description', '')}"
#     logger.info("Returning job document")
#
#     return {
#         "job_id": job_id,
#         "text": text,
#         "metadata": {
#             "domain": job.get("domain", ""),
#             "title": job.get("title", ""),
#             "employerId": employer_id
#         }
#     }


@mcp.tool()
def fetch_job(job_id: str, employer_id: str) -> dict:
    import sys
    print("🪵 [DEBUG] fetch_job called with:", file=sys.stdout, flush=True)
    print("🪵 [DEBUG] job_id =", job_id, file=sys.stdout, flush=True)
    print("🪵 [DEBUG] employer_id =", employer_id, file=sys.stdout, flush=True)


    job = jobs_collection.find_one({
        "_id": ObjectId(job_id),
        "employerId": employer_id
    })

    # print("✅ [DEBUG] Job found in DB", file=sys.stdout, flush=True)

    text = f"Title: {job.get('title', '')}\nDescription: {job.get('description', '')}"

    return {
        "job_id": job_id,
        "text": text,
        "metadata": {
            "domain": job.get("domain", ""),
            "title": job.get("title", ""),
            "employerId": employer_id
        }
    }



if __name__ == "__main__":
    mcp.run(transport="streamable-http")










































# from mcp.server.fastmcp import FastMCP
# from pymongo import MongoClient
#
# mcp = FastMCP(name="mongo")
#
# mongo = MongoClient("mongodb://localhost:27017/")
# db = mongo["your_db_name"]
# jobs_collection = db["jobs"]
#
# @mcp.tool()
# def fetch_job(job_id: str) -> dict:
#     """
#     MCP tool: fetch job doc from MongoDB.
#     Returns: {"job_id": ..., "text": "<title>\n<desc>", "metadata": {...}}
#     """
#     job = jobs_collection.find_one({"_id": job_id})
#     if not job:
#         return {"error": f"Job {job_id} not found."}
#     text = f"{job.get('title','')} \n {job.get('description','')}"
#     return {
#         "job_id": job_id,
#         "text": text,
#         "metadata": {"domain": job.get("domain", "")}
#     }
#
# if __name__ == "__main__":
#     mcp.run(transport="streamable-http")
