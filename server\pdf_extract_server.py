from mcp.server.fastmcp import FastMCP
import fitz  # PyMuPDF
import sys

mcp = FastMCP("pdf_extract")

@mcp.tool()
def extract_job_from_pdf(file_path: str) -> dict:
    """
    Extracts text from a job description PDF file.

    Args:
        file_path (str): Path to the PDF file

    Returns:
        dict: { "text": full_text }
    """
    doc = fitz.open(file_path)
    full_text = "\n".join([page.get_text() for page in doc])

    print("✅ [PDF Extract] Extracted text from:", file_path)
    print("----------- TEXT START -------------")
    print(full_text[:50])  # Show only first 1000 chars
    print("------------ TEXT END --------------")
    sys.stdout.flush()

    with open("extract_log.txt", "a") as f:
        f.write(f"\n[Extracted from {file_path}]\n")
        f.write(full_text + "\n\n")

    return {"text": full_text}

if __name__ == "__main__":
    mcp.run(transport="stdio")
